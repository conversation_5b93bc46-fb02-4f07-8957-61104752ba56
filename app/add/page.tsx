'use client';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';

export default function Add() {
  return (
    <div className="min-h-screen bg-background pt-[60px] pb-20">
      <div className="max-w-md mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Create New Post</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              This page is accessed through the Add button in the bottom navigation,
              which opens the AddPostModal component.
            </p>
            <Button onClick={() => window.history.back()}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
