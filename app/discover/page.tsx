'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

export default function Discover() {
  const [searchQuery, setSearchQuery] = useState('');

  const trendingTags = [
    { name: 'Photography', count: 1234 },
    { name: 'Digital Art', count: 987 },
    { name: 'NFT', count: 756 },
    { name: 'Vintage', count: 543 },
    { name: 'Collectibles', count: 432 },
    { name: 'Gaming', count: 321 },
  ];

  const featuredCreators = [
    { name: 'Alex Designer', followers: '12.5K', avatar: null },
    { name: 'Sarah Artist', followers: '8.9K', avatar: null },
    { name: '<PERSON>', followers: '6.2K', avatar: null },
    { name: '<PERSON> Creator', followers: '4.8K', avatar: null },
  ];

  return (
    <div className="min-h-screen bg-background pt-[60px] pb-20">
      <div className="max-w-md mx-auto p-4 space-y-6">
        {/* 搜索栏 */}
        <div className="space-y-4">
          <div className="relative">
            <Input
              type="text"
              placeholder="Search for posts, creators, or tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* 标签页 */}
        <Tabs defaultValue="trending" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="trending">Trending</TabsTrigger>
            <TabsTrigger value="creators">Creators</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>

          <TabsContent value="trending" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Trending Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {trendingTags.map((tag) => (
                    <Badge key={tag.name} variant="secondary" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
                      #{tag.name} ({tag.count})
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="creators" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Featured Creators</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {featuredCreators.map((creator) => (
                    <div key={creator.name} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                          <div className="w-6 h-6 bg-muted-foreground rounded-full"></div>
                        </div>
                        <div>
                          <p className="font-medium text-sm">{creator.name}</p>
                          <p className="text-muted-foreground text-xs">{creator.followers} followers</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">Follow</Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Browse Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {['Art', 'Photography', 'Music', 'Gaming', 'Sports', 'Fashion', 'Technology', 'Nature'].map((category) => (
                    <Button key={category} variant="outline" className="h-12">
                      {category}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
