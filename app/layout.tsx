import type { Metadata } from "next";
import "./globals.css";
import BottomNavigation from "../components/BottomNavigation";
import TopNavigation from "../components/TopNavigation";

// 完全使用系统字体，避免网络请求

export const metadata: Metadata = {
  title: "Whisp - Social Platform",
  description: "A modern social platform built with Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        <TopNavigation />
        {children}
        <BottomNavigation />
      </body>
    </html>
  );
}
