import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import BottomNavigation from "../components/BottomNavigation";
import TopNavigation from "../components/TopNavigation";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Whisp - Social Platform",
  description: "A modern social platform built with Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        <TopNavigation />
        {children}
        <BottomNavigation />
      </body>
    </html>
  );
}
