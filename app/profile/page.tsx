'use client';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function Profile() {
  const userStats = [
    { label: 'Posts', value: '24' },
    { label: 'Followers', value: '1.2K' },
    { label: 'Following', value: '456' },
    { label: 'Likes', value: '3.4K' },
  ];

  const userPosts = [
    { id: 1, title: 'Vintage Camera', price: 1200, likes: 45 },
    { id: 2, title: 'Digital Art Piece', price: 800, likes: 32 },
    { id: 3, title: 'Collectible Card', price: 150, likes: 28 },
    { id: 4, title: 'Photography Print', price: 300, likes: 67 },
  ];

  const achievements = [
    { name: 'First Sale', description: 'Made your first sale' },
    { name: 'Popular Creator', description: 'Reached 1K followers' },
    { name: 'Top Seller', description: 'Sold 10+ items this month' },
  ];

  return (
    <div className="min-h-screen bg-background pt-[60px] pb-20">
      <div className="max-w-md mx-auto p-4 space-y-6">
        {/* 用户信息卡片 */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              {/* 头像 */}
              <Avatar className="h-20 w-20">
                <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                  JD
                </AvatarFallback>
              </Avatar>

              {/* 用户名和简介 */}
              <div className="text-center space-y-2">
                <h1 className="text-xl font-semibold">John Doe</h1>
                <p className="text-muted-foreground text-sm">
                  Digital artist & collector passionate about vintage items and modern art.
                </p>
                <div className="flex justify-center space-x-2">
                  <Badge variant="secondary">Artist</Badge>
                  <Badge variant="secondary">Collector</Badge>
                </div>
              </div>

              {/* 统计数据 */}
              <div className="grid grid-cols-4 gap-4 w-full">
                {userStats.map((stat) => (
                  <div key={stat.label} className="text-center">
                    <p className="font-semibold text-lg">{stat.value}</p>
                    <p className="text-muted-foreground text-xs">{stat.label}</p>
                  </div>
                ))}
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-2 w-full">
                <Button className="flex-1">Edit Profile</Button>
                <Button variant="outline" className="flex-1">Share</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 标签页内容 */}
        <Tabs defaultValue="posts" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="posts">Posts</TabsTrigger>
            <TabsTrigger value="collections">Collections</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
          </TabsList>

          <TabsContent value="posts" className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              {userPosts.map((post) => (
                <Card key={post.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-3">
                    <div className="aspect-square bg-muted rounded-lg mb-2 flex items-center justify-center">
                      <div className="grid grid-cols-4 gap-1 w-full h-full p-1">
                        {Array.from({ length: 16 }).map((_, i) => (
                          <div key={i} className="bg-gray-200 rounded-sm"></div>
                        ))}
                      </div>
                    </div>
                    <h3 className="font-medium text-sm truncate">{post.title}</h3>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-green-600 font-semibold text-sm">${post.price}</span>
                      <span className="text-muted-foreground text-xs">♥ {post.likes}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="collections" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">My Collections</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['Vintage Cameras', 'Digital Art', 'Photography Prints'].map((collection) => (
                    <div key={collection} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-muted rounded-lg"></div>
                        <div>
                          <p className="font-medium text-sm">{collection}</p>
                          <p className="text-muted-foreground text-xs">12 items</p>
                        </div>
                      </div>
                      <Button size="sm" variant="ghost">View</Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="achievements" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Achievements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {achievements.map((achievement) => (
                    <div key={achievement.name} className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                        <span className="text-yellow-600">🏆</span>
                      </div>
                      <div>
                        <p className="font-medium text-sm">{achievement.name}</p>
                        <p className="text-muted-foreground text-xs">{achievement.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
