'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export default function Wallet() {
  const [balance] = useState(2847.50);

  const transactions = [
    { id: 1, type: 'sale', item: 'Vintage Camera', amount: 1200, date: '2024-01-15', status: 'completed' },
    { id: 2, type: 'purchase', item: 'Digital Art Piece', amount: -800, date: '2024-01-14', status: 'completed' },
    { id: 3, type: 'sale', item: 'Photography Print', amount: 300, date: '2024-01-13', status: 'completed' },
    { id: 4, type: 'purchase', item: 'Collectible Card', amount: -150, date: '2024-01-12', status: 'pending' },
  ];

  const earnings = [
    { period: 'Today', amount: 0 },
    { period: 'This Week', amount: 450 },
    { period: 'This Month', amount: 1850 },
    { period: 'All Time', amount: 12340 },
  ];

  return (
    <div className="min-h-screen bg-background pt-[60px] pb-20">
      <div className="max-w-md mx-auto p-4 space-y-6">
        {/* 余额卡片 */}
        <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div>
                <p className="text-blue-100 text-sm">Total Balance</p>
                <h1 className="text-3xl font-bold">${balance.toLocaleString()}</h1>
              </div>

              <div className="flex space-x-2">
                <Button variant="secondary" size="sm" className="flex-1">
                  Withdraw
                </Button>
                <Button variant="outline" size="sm" className="flex-1 text-white border-white hover:bg-white hover:text-blue-600">
                  Add Funds
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 收益统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Earnings Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {earnings.map((earning) => (
                <div key={earning.period} className="text-center p-3 bg-muted rounded-lg">
                  <p className="text-muted-foreground text-xs">{earning.period}</p>
                  <p className="font-semibold text-lg text-green-600">
                    ${earning.amount.toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 标签页 */}
        <Tabs defaultValue="transactions" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Transactions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions.map((transaction, index) => (
                    <div key={transaction.id}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            transaction.type === 'sale' ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {transaction.type === 'sale' ? (
                              <span className="text-green-600">↗</span>
                            ) : (
                              <span className="text-red-600">↙</span>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-sm">{transaction.item}</p>
                            <p className="text-muted-foreground text-xs">{transaction.date}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`font-semibold text-sm ${
                            transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount)}
                          </p>
                          <Badge
                            variant={transaction.status === 'completed' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {transaction.status}
                          </Badge>
                        </div>
                      </div>
                      {index < transactions.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* 销售趋势 */}
                  <div>
                    <h3 className="font-medium text-sm mb-3">Sales Trend</h3>
                    <div className="h-32 bg-muted rounded-lg flex items-end justify-center p-4">
                      <div className="flex items-end space-x-2 h-full">
                        {[40, 65, 45, 80, 55, 90, 70].map((height, i) => (
                          <div
                            key={i}
                            className="bg-blue-500 rounded-t w-6"
                            style={{ height: `${height}%` }}
                          ></div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 热门类别 */}
                  <div>
                    <h3 className="font-medium text-sm mb-3">Top Categories</h3>
                    <div className="space-y-2">
                      {[
                        { name: 'Photography', percentage: 45 },
                        { name: 'Digital Art', percentage: 30 },
                        { name: 'Collectibles', percentage: 25 },
                      ].map((category) => (
                        <div key={category.name} className="flex items-center justify-between">
                          <span className="text-sm">{category.name}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full bg-blue-500 rounded-full"
                                style={{ width: `${category.percentage}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-muted-foreground">{category.percentage}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
