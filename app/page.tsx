import PostCard from '../components/PostCard';
import { mockPosts } from '../data/mockPosts';

export default function Home() {
  return (
    <div className="min-h-screen bg-background pt-[60px] pb-20">
      {/* 信息流容器 */}
      <div className="max-w-md mx-auto">
        {/* 帖子列表 */}
        <div className="space-y-0">
          {mockPosts.map((post) => (
            <PostCard
              key={post.id}
              id={post.id}
              username={post.username}
              userAvatar={post.userAvatar}
              timeAgo={post.timeAgo}
              title={post.title}
              content={post.content}
              price={post.price}
              commentCount={post.commentCount}
              imageUrl={post.imageUrl}
              comments={post.comments}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
