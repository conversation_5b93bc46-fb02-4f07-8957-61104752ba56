'use client';

import Image from 'next/image';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

interface PostCardProps {
  id: string;
  username: string;
  userAvatar?: string;
  timeAgo: string;
  title: string;
  content: string;
  price: number;
  commentCount: number;
  imageUrl?: string;
  comments?: Array<{
    id: string;
    username: string;
    content: string;
    avatar?: string;
  }>;
}

export default function PostCard({
  id,
  username,
  userAvatar,
  timeAgo,
  title,
  content,
  price,
  commentCount,
  imageUrl,
  comments = []
}: PostCardProps) {
  // 只显示1个评论
  const displayedComments = comments.slice(0, 1);

  return (
    <Card className="mb-4 border-0 border-b border-gray-100 rounded-none shadow-none">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* 用户头像 */}
            <Avatar className="h-10 w-10">
              <AvatarImage src={userAvatar} alt={username} />
              <AvatarFallback className="bg-gray-200">
                <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
              </AvatarFallback>
            </Avatar>

            {/* 用户名和时间 */}
            <div>
              <p className="font-medium text-foreground text-sm">{username}</p>
              <p className="text-muted-foreground text-xs">{timeAgo}</p>
            </div>
          </div>

          {/* 更多按钮 */}
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <svg className="w-5 h-5 text-muted-foreground" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-4">

        {/* 图片内容区域 */}
        <div className="w-full h-64 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
          {imageUrl ? (
            <Image
              src={imageUrl}
              alt={title}
              width={400}
              height={256}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="grid grid-cols-8 gap-1 w-full h-full p-2">
              {Array.from({ length: 64 }).map((_, i) => (
                <div key={i} className="bg-gray-200 rounded-sm"></div>
              ))}
            </div>
          )}
        </div>

        {/* 价格和互动按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 价格标签 */}
            <Badge variant="secondary" className="bg-green-50 text-green-700 hover:bg-green-100">
              <div className="w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-b-[8px] border-b-green-500 mr-1"></div>
              ${price}
            </Badge>

            {/* 评论按钮 */}
            <Button variant="ghost" size="sm" className="h-8 px-2 text-muted-foreground">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span className="text-sm">{commentCount}</span>
            </Button>

            {/* 分享按钮 */}
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-muted-foreground">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
            </Button>
          </div>

          {/* 购买按钮 */}
          <Button className="bg-green-500 hover:bg-green-600 text-white rounded-full px-6">
            Buy
          </Button>
        </div>

        {/* 标题和内容 */}
        <div>
          <h3 className="font-semibold text-foreground mb-2 text-sm">{title}</h3>
          <p className="text-muted-foreground text-sm leading-relaxed">{content}</p>
        </div>

        {/* 评论区域 - 只显示1个评论 */}
        {displayedComments.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              {displayedComments.map((comment) => (
                <div key={comment.id} className="flex items-start space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={comment.avatar} alt={comment.username} />
                    <AvatarFallback className="bg-gray-200">
                      <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <p className="text-muted-foreground text-xs font-medium">{comment.username}</p>
                    <p className="text-foreground text-sm">{comment.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
