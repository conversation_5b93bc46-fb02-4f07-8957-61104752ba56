'use client';

import { useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import AddPostModal from './AddPostModal';

interface NavItem {
  name: string;
  path: string;
  icon: string; // SVG 文件路径
  activeIcon: string; // 激活状态的 SVG 文件路径
}

const navItems: NavItem[] = [
  {
    name: 'Home',
    path: '/',
    icon: '/icons/default/Home.svg',
    activeIcon: '/icons/active/Home.svg',
  },
  {
    name: 'Discover',
    path: '/discover',
    icon: '/icons/default/Search.svg',
    activeIcon: '/icons/active/Search.svg',
  },
  {
    name: 'Add',
    path: '/add',
    icon: '/icons/default/Add.svg',
    activeIcon: '/icons/active/Add.svg',
  },
  {
    name: 'Wallet',
    path: '/wallet',
    icon: '/icons/default/Wallet.svg',
    activeIcon: '/icons/active/Wallet.svg',
  },
  {
    name: 'Profile',
    path: '/profile',
    icon: '/icons/default/Profile.svg',
    activeIcon: '/icons/active/Profile.svg',
  },
];

export default function BottomNavigation() {
  const pathname = usePathname();
  const router = useRouter();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleNavigation = (path: string) => {
    if (path === '/add') {
      setIsAddModalOpen(true);
    } else {
      router.push(path);
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border z-50">
      <div className="flex items-center w-[375px] h-[82px] px-4 py-4 mx-auto">
        {/* 左侧两个按钮 */}
        <div className="flex flex-1 justify-around">
          {navItems.slice(0, 2).map((item) => {
            const isActive = pathname === item.path;
            const iconSrc = isActive ? item.activeIcon : item.icon;
            return (
              <Button
                key={item.name}
                variant="ghost"
                onClick={() => handleNavigation(item.path)}
                className={`flex flex-col h-auto p-2 transition-colors duration-200 ${
                  isActive
                    ? 'text-foreground'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <Image
                  src={iconSrc}
                  alt={item.name}
                  width={24}
                  height={24}
                  className="mb-1"
                  priority
                />
                <span className="text-sm font-normal leading-[22px] tracking-normal">{item.name}</span>
              </Button>
            );
          })}
        </div>

        {/* 中间的 Add 按钮 */}
        {(() => {
          const addItem = navItems[2]; // Add 按钮
          const isActive = pathname === addItem.path;
          const iconSrc = isActive ? addItem.activeIcon : addItem.icon;
          return (
            <Button
              key={addItem.name}
              variant="ghost"
              onClick={() => handleNavigation(addItem.path)}
              className="h-14 w-14 p-0 mx-2 transition-colors duration-200"
            >
              <Image
                src={iconSrc}
                alt={addItem.name}
                width={56}
                height={56}
                priority
              />
            </Button>
          );
        })()}

        {/* 右侧两个按钮 */}
        <div className="flex flex-1 justify-around">
          {navItems.slice(3, 5).map((item) => {
            const isActive = pathname === item.path;
            const iconSrc = isActive ? item.activeIcon : item.icon;
            return (
              <Button
                key={item.name}
                variant="ghost"
                onClick={() => handleNavigation(item.path)}
                className={`flex flex-col h-auto p-2 transition-colors duration-200 ${
                  isActive
                    ? 'text-foreground'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <Image
                  src={iconSrc}
                  alt={item.name}
                  width={24}
                  height={24}
                  className="mb-1"
                  priority
                />
                <span className="text-sm font-normal leading-[22px] tracking-normal">{item.name}</span>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Add Post Modal */}
      <AddPostModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
      />
    </div>
  );
}
