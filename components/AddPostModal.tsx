'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

interface AddPostModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AddPostModal({ isOpen, onClose }: AddPostModalProps) {
  const [activeTab, setActiveTab] = useState<'upload' | 'generate'>('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploaded, setIsUploaded] = useState(false);
  const [title, setTitle] = useState('');
  const [caption, setCaption] = useState('');
  const [titleError, setTitleError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (file: File) => {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/mov'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a valid image (JPEG, PNG, GIF, WebP) or video (MP4, WebM, MOV) file.');
      return;
    }

    // 检查文件大小 (100MB)
    const maxSize = 100 * 1024 * 1024; // 100MB in bytes
    if (file.size > maxSize) {
      alert('File size must be less than 100MB.');
      return;
    }

    setSelectedFile(file);

    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // 直接设置为已上传状态，跳过上传步骤
    setIsUploaded(true);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    // 重置表单
    setTitle('');
    setCaption('');
    setTitleError('');
    setIsUploaded(false);
  };

  const validateAndPost = () => {
    // 验证标题
    if (!title.trim()) {
      setTitleError('Title is required');
      return;
    }
    setTitleError('');

    // 模拟发布
    alert(`Post created successfully!\nTitle: ${title}\nCaption: ${caption}`);
    removeFile();
    onClose();
  };



  const isVideo = selectedFile?.type.startsWith('video/');
  const isImage = selectedFile?.type.startsWith('image/');

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-[95vh] rounded-t-3xl">
        <SheetHeader className="text-center">
          {/* 顶部拖拽指示器 */}
          <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>

          {/* 隐藏的标题，用于可访问性 */}
          <SheetTitle className="sr-only">Create New Post</SheetTitle>

          {/* 标签切换 */}
          <div className="flex mb-8">
            <Button
              onClick={() => setActiveTab('upload')}
              variant={activeTab === 'upload' ? 'default' : 'secondary'}
              className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-colors ${
                activeTab === 'upload'
                  ? 'bg-gray-900 text-white hover:bg-gray-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Upload
            </Button>
            <Button
              onClick={() => setActiveTab('generate')}
              variant={activeTab === 'generate' ? 'default' : 'secondary'}
              className={`flex-1 py-3 px-6 rounded-full text-sm font-medium transition-colors ml-2 ${
                activeTab === 'generate'
                  ? 'bg-gray-900 text-white hover:bg-gray-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Generate
            </Button>
          </div>
        </SheetHeader>

        {/* Upload 标签内容 */}
        {activeTab === 'upload' && (
          <div className="text-center px-4">
            {/* 隐藏的文件输入 */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*,video/*"
              onChange={handleFileInputChange}
              className="hidden"
            />

            {/* 文件预览或上传区域 */}
            {selectedFile ? (
              <div className="space-y-6">
                {/* 文件预览 - 完全占满虚线框位置 */}
                <div className="rounded-2xl mb-6 relative overflow-hidden" style={{ height: '320px' }}>
                  {/* 预览内容区域 - 棋盘格透明背景，完全填满容器 */}
                  <div
                    className="w-full h-full rounded-2xl overflow-hidden relative"
                    style={{
                      backgroundImage: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
                      backgroundSize: '20px 20px',
                      backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
                    }}
                  >
                    {isImage && previewUrl && (
                      <Image
                        src={previewUrl}
                        alt="Preview"
                        fill
                        className="object-contain rounded-2xl"
                      />
                    )}

                    {isVideo && previewUrl && (
                      <video
                        src={previewUrl}
                        controls
                        className="w-full h-full object-contain rounded-2xl"
                      />
                    )}

                    {/* 铅笔编辑图标 - 放在预览内容上方 */}
                    <Button
                      onClick={removeFile}
                      variant="secondary"
                      size="sm"
                      className="absolute top-3 right-3 h-10 w-10 p-0 rounded-full bg-gray-600 hover:bg-gray-700 text-white z-10"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </Button>
                  </div>
                </div>

                {/* 直接显示编辑表单 */}
                {isUploaded && (
                  <div className="space-y-4">

                    {/* 标题输入 */}
                    <div className="space-y-3">
                      <input
                        type="text"
                        placeholder="Enter title"
                        value={title}
                        onChange={(e) => {
                          setTitle(e.target.value);
                          if (titleError) setTitleError('');
                        }}
                        className="w-full text-lg font-medium text-gray-900 placeholder-gray-400 bg-transparent border-none outline-none focus:outline-none p-0"
                        style={{ fontSize: '18px' }}
                      />
                      {titleError && (
                        <p className="text-sm text-red-500">{titleError}</p>
                      )}
                    </div>

                    {/* 描述输入 */}
                    <div className="space-y-3">
                      <textarea
                        placeholder="Write a caption..."
                        value={caption}
                        onChange={(e) => setCaption(e.target.value)}
                        rows={3}
                        className="w-full text-base text-gray-900 placeholder-gray-400 bg-transparent border-none outline-none focus:outline-none resize-none p-0"
                        style={{ fontSize: '16px' }}
                      />
                    </div>

                    {/* 发布按钮 */}
                    <Button
                      onClick={validateAndPost}
                      className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3 rounded-lg font-medium"
                    >
                      Post
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              /* 拖拽上传区域 */
              <div
                className={`border-2 border-dashed rounded-2xl p-12 mb-6 transition-colors ${
                  isDragging
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {/* 上传图标 */}
                <div className="w-16 h-16 mx-auto mb-6 relative">
                  <div className="w-16 h-16 border-2 border-blue-500 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  {/* 箭头指示器 */}
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                  </div>
                </div>

                {/* 标题 */}
                <SheetTitle className="text-lg font-semibold text-gray-900 mb-3">
                  {isDragging ? 'Drop your file here' : 'Upload photo and video'}
                </SheetTitle>

                {/* 描述文字 */}
                <p className="text-gray-500 text-sm mb-2">
                  Drag or select a file to mint an NFT.
                </p>
                <p className="text-gray-500 text-sm mb-8">
                  Supports: JPEG, PNG, GIF, WebP, MP4, WebM, MOV • Max 100 MB
                </p>

                {/* 浏览文件按钮 */}
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFileUpload();
                  }}
                  className="bg-gray-900 text-white px-8 py-3 rounded-full font-medium hover:bg-gray-800 active:bg-gray-700 active:scale-95 transition-all duration-150"
                >
                  Browse file
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Generate 标签内容 */}
        {activeTab === 'generate' && (
          <div className="text-center py-12 px-4">
            <div className="w-16 h-16 mx-auto mb-6 bg-purple-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <SheetTitle className="text-lg font-semibold text-gray-900 mb-3">
              Generate with AI
            </SheetTitle>
            <p className="text-gray-500 text-sm mb-8">
              Create unique content using artificial intelligence
            </p>
            <Button className="bg-purple-600 text-white px-8 py-3 rounded-full font-medium hover:bg-purple-700 transition-colors">
              Start Generating
            </Button>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
