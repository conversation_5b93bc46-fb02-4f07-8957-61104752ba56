'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';

export default function TopNavigation() {
  const [activeItem, setActiveItem] = useState<string | null>(null);

  const handleItemClick = (itemName: string) => {
    setActiveItem(activeItem === itemName ? null : itemName);
  };

  return (
    <div className="fixed top-0 left-0 right-0 bg-background border-b border-border z-50">
      <div className="flex items-center justify-between w-[375px] h-[60px] px-4 mx-auto">
        {/* 左侧用户头像占位符 */}
        <Avatar className="h-10 w-10">
          <AvatarFallback className="bg-muted">
            <div className="w-6 h-6 bg-muted-foreground rounded-full"></div>
          </AvatarFallback>
        </Avatar>

        {/* 右侧按钮组 */}
        <div className="flex items-center gap-2">
          {/* Menu 按钮 */}
          <Button
            variant={activeItem === 'menu' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => handleItemClick('menu')}
            className="h-10 w-10 p-0"
          >
            <Image
              src={activeItem === 'menu' ? '/icons/active/Menu.svg' : '/icons/default/Menu.svg'}
              alt="Menu"
              width={24}
              height={24}
              priority
            />
          </Button>

          {/* Bell 按钮 */}
          <Button
            variant={activeItem === 'bell' ? 'secondary' : 'ghost'}
            size="sm"
            onClick={() => handleItemClick('bell')}
            className="h-10 w-10 p-0"
          >
            <Image
              src={activeItem === 'bell' ? '/icons/active/Bell.svg' : '/icons/default/Bell.svg'}
              alt="Notifications"
              width={24}
              height={24}
              priority
            />
          </Button>
        </div>
      </div>
    </div>
  );
}
